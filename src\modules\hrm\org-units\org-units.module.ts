import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from '@/modules/auth/auth.module';
import { EmployeesModule } from '@/modules/hrm/employees/employees.module';
import { Department } from './entities/department.entity';
import { DepartmentController } from './controllers/department.controller';
import { DepartmentService } from './services/department.service';
import { DepartmentRepository } from './repositories/department.repository';
import { DepartmentMembersService } from './services/department-members.service';
import { DepartmentMembersController } from './controllers/department-members.controller';
import { DepartmentTreeService } from './services/department-tree.service';
import { DepartmentTreeController } from './controllers/department-tree.controller';

/**
 * Module quản lý đơn vị tổ chức
 * UPDATED: Import EmployeesModule để sử dụng EmployeeRepository thay vì UserRepository
 */
@Global()
@Module({
  imports: [
    AuthModule,
    EmployeesModule, // Thêm để sử dụng EmployeeRepository
    TypeOrmModule.forFeature([Department])
  ],
  controllers: [
    DepartmentController,
    DepartmentMembersController,
    DepartmentTreeController,
  ],
  providers: [
    DepartmentService,
    DepartmentRepository,
    DepartmentMembersService,
    DepartmentTreeService,
  ],
  exports: [
    DepartmentService,
    DepartmentRepository,
    DepartmentMembersService,
    DepartmentTreeService
  ],
})
export class OrgUnitsModule {}
